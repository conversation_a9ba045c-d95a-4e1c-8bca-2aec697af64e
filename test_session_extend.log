2025-06-19 18:45:35.887 | INFO     | __main__:<module>:87 - 开始测试ANA会话延长功能修复...
2025-06-19 18:45:35.888 | INFO     | __main__:test_session_extend_payload:62 - === 修复后的会话延长请求payload ===
2025-06-19 18:45:35.888 | INFO     | __main__:test_session_extend_payload:63 - URL: https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/common/password_input.xhtml?rand=20250617133412R-d8KXG7hw
2025-06-19 18:45:35.889 | INFO     | __main__:test_session_extend_payload:64 - Headers:
2025-06-19 18:45:35.889 | INFO     | __main__:test_session_extend_payload:66 -   Accept: */*
2025-06-19 18:45:35.890 | INFO     | __main__:test_session_extend_payload:66 -   Accept-Encoding: gzip, deflate, br, zstd
2025-06-19 18:45:35.890 | INFO     | __main__:test_session_extend_payload:66 -   Accept-Language: zh-C<PERSON>,zh;q=0.9,en;q=0.8
2025-06-19 18:45:35.891 | INFO     | __main__:test_session_extend_payload:66 -   Connection: keep-alive
2025-06-19 18:45:35.892 | INFO     | __main__:test_session_extend_payload:66 -   Content-Type: application/x-www-form-urlencoded;charset=UTF-8
2025-06-19 18:45:35.892 | INFO     | __main__:test_session_extend_payload:66 -   Faces-Request: partial/ajax
2025-06-19 18:45:35.893 | INFO     | __main__:test_session_extend_payload:66 -   Host: aswbe-i.ana.co.jp
2025-06-19 18:45:35.893 | INFO     | __main__:test_session_extend_payload:66 -   Origin: https://aswbe-i.ana.co.jp
2025-06-19 18:45:35.893 | INFO     | __main__:test_session_extend_payload:66 -   Referer: https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/common/password_input.xhtml?rand=20250617133412R-d8KXG7hw
2025-06-19 18:45:35.894 | INFO     | __main__:test_session_extend_payload:66 -   Sec-Fetch-Dest: empty
2025-06-19 18:45:35.894 | INFO     | __main__:test_session_extend_payload:66 -   Sec-Fetch-Mode: cors
2025-06-19 18:45:35.895 | INFO     | __main__:test_session_extend_payload:66 -   Sec-Fetch-Site: same-origin
2025-06-19 18:45:35.895 | INFO     | __main__:test_session_extend_payload:66 -   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-19 18:45:35.896 | INFO     | __main__:test_session_extend_payload:66 -   x-dtpc: 1$534850600_325h6vDPMMVBRMAMOFFPKHEOWPJNWRDARLAQCR-0e0
2025-06-19 18:45:35.896 | INFO     | __main__:test_session_extend_payload:66 -   sec-ch-ua: "Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
2025-06-19 18:45:35.897 | INFO     | __main__:test_session_extend_payload:66 -   sec-ch-ua-mobile: ?0
2025-06-19 18:45:35.897 | INFO     | __main__:test_session_extend_payload:66 -   sec-ch-ua-platform: "Windows"
2025-06-19 18:45:35.897 | INFO     | __main__:test_session_extend_payload:68 - Payload:
2025-06-19 18:45:35.898 | INFO     | __main__:test_session_extend_payload:71 -   sessionKeeperContainer:j_idt170: sessionKeeperContainer:j_idt170
2025-06-19 18:45:35.899 | INFO     | __main__:test_session_extend_payload:71 -   sessionKeeperContainer:j_idt170_operationTicket: xylhwx+b0f639559e987898b6c68a3af6cc5259~E2sfxS4F7E...
2025-06-19 18:45:35.899 | INFO     | __main__:test_session_extend_payload:71 -   sessionKeeperContainer:j_idt170_cmnPageTicket: 5
2025-06-19 18:45:35.899 | INFO     | __main__:test_session_extend_payload:71 -   javax.faces.ViewState: RhETCobTeXnIj3X+ZnziZngRoSz6Bq+XbNY1V7dslUWO/0FIyi...
2025-06-19 18:45:35.900 | INFO     | __main__:test_session_extend_payload:71 -   javax.faces.source: sessionKeeperContainer:cmnSessionKeepingButton
2025-06-19 18:45:35.900 | INFO     | __main__:test_session_extend_payload:71 -   javax.faces.partial.event: action
2025-06-19 18:45:35.901 | INFO     | __main__:test_session_extend_payload:71 -   javax.faces.partial.execute: sessionKeeperContainer:cmnSessionKeepingButton
2025-06-19 18:45:35.901 | INFO     | __main__:test_session_extend_payload:71 -   javax.faces.behavior.event: action
2025-06-19 18:45:35.902 | INFO     | __main__:test_session_extend_payload:71 -   javax.faces.partial.ajax: true
2025-06-19 18:45:35.902 | INFO     | __main__:test_session_extend_payload:71 -   sessionKeeperContainer:cmnSessionKeepingButton: 延長
2025-06-19 18:45:35.903 | INFO     | __main__:test_session_extend_payload:73 - === 关键修复点 ===
2025-06-19 18:45:35.903 | INFO     | __main__:test_session_extend_payload:74 - 1. 添加了JSF AJAX必需的字段:
2025-06-19 18:45:35.904 | INFO     | __main__:test_session_extend_payload:75 -    - javax.faces.source
2025-06-19 18:45:35.904 | INFO     | __main__:test_session_extend_payload:76 -    - javax.faces.partial.event
2025-06-19 18:45:35.905 | INFO     | __main__:test_session_extend_payload:77 -    - javax.faces.partial.execute
2025-06-19 18:45:35.905 | INFO     | __main__:test_session_extend_payload:78 -    - javax.faces.behavior.event
2025-06-19 18:45:35.906 | INFO     | __main__:test_session_extend_payload:79 -    - javax.faces.partial.ajax
2025-06-19 18:45:35.906 | INFO     | __main__:test_session_extend_payload:80 - 2. 添加了按钮本身的字段:
2025-06-19 18:45:35.907 | INFO     | __main__:test_session_extend_payload:81 -    - sessionKeeperContainer:cmnSessionKeepingButton
2025-06-19 18:45:35.907 | INFO     | __main__:test_session_extend_payload:82 - 3. 保持了正确的Content-Type和Faces-Request头
2025-06-19 18:45:35.908 | INFO     | __main__:<module>:89 - 测试完成！
