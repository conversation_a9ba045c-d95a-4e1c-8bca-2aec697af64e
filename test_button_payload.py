#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的延长按钮payload
基于真实的HTML按钮结构验证请求格式
"""

from loguru import logger
import json

def test_button_payload():
    """测试基于真实HTML按钮的payload构建"""
    
    logger.info("=== 测试延长按钮payload构建 ===")
    
    # 模拟从页面提取的表单信息
    form_id = "sessionKeeperContainer:j_idt170"
    operation_ticket = "xylhwx+b0f639559e987898b6c68a3af6cc5259~E2sfxS4F7EyykbQyViykJ7uDML_JmQzz7CTvsZcR!1750134816402.aere-xml-controller-8467bf7fd6-fssk4"
    page_ticket = "5"
    view_state = "RhETCobTeXnIj3X+ZnziZngRoSz6Bq+XbNY1V7dslUWO/0FIyismhombOKgA/apQ..."
    
    # 构建修复后的payload
    payload = {
        form_id: form_id,
        f'{form_id}_operationTicket': operation_ticket,
        f'{form_id}_cmnPageTicket': page_ticket,
        'javax.faces.ViewState': view_state,
        # 关键的JSF AJAX字段（根据真实按钮HTML修正）
        'javax.faces.source': 'sessionKeeperContainer:cmnSessionKeepingButton',
        'javax.faces.partial.event': 'action',  # 从onclick看是action事件
        'javax.faces.partial.execute': 'sessionKeeperContainer:cmnSessionKeepingButton',
        'javax.faces.behavior.event': 'action',
        'javax.faces.partial.ajax': 'true',
        # 添加按钮本身的字段
        'sessionKeeperContainer:cmnSessionKeepingButton': '延長'
    }
    
    logger.info("构建的payload:")
    for key, value in payload.items():
        display_value = value[:50] + "..." if len(str(value)) > 50 else value
        logger.info(f"  {key}: {display_value}")
    
    return payload

def compare_with_real_html():
    """对比真实HTML按钮和我们的请求"""
    
    logger.info("=== HTML按钮分析 ===")
    
    # 真实的HTML按钮信息
    button_info = {
        "id": "sessionKeeperContainer:cmnSessionKeepingButton",
        "name": "sessionKeeperContainer:cmnSessionKeepingButton", 
        "value": "延長",
        "type": "submit",
        "onclick_function": "mojarra.ab()",
        "event_type": "action",
        "ajax_callbacks": ["Asw.doCommonAjaxCallback", "Asw.SessionKeeper.doSessionKeep"]
    }
    
    logger.info("真实按钮信息:")
    for key, value in button_info.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("\n✅ 修复要点:")
    logger.info("1. 按钮ID是 sessionKeeperContainer:cmnSessionKeepingButton（不是表单ID+按钮名）")
    logger.info("2. partial.event 应该是 'action'（不是 'click'）")
    logger.info("3. 需要包含按钮本身的 name=value 对")
    logger.info("4. onclick 调用 mojarra.ab() 进行JSF AJAX提交")

def test_timing():
    """测试修复后的时间参数"""
    
    logger.info("=== 时间参数修复 ===")
    
    # JavaScript中的真实参数
    js_params = {
        "sessionKeepTime": 520,  # 8分40秒
        "sessionTimeout": 570,   # 9分30秒
        "maxExtensions": 9       # 最多延长9次
    }
    
    logger.info("JavaScript SessionKeeper参数:")
    for key, value in js_params.items():
        if key.endswith("Time") or key.endswith("Timeout"):
            minutes = value // 60
            seconds = value % 60
            logger.info(f"  {key}: {value}秒 ({minutes}分{seconds}秒)")
        else:
            logger.info(f"  {key}: {value}")
    
    logger.info("\n✅ 时间修复:")
    logger.info("- 等待时间从8.5分钟改为8分40秒（520秒）")
    logger.info("- 与浏览器JavaScript完全一致")
    logger.info("- 最多可延长9次，总计125分钟")

def test_headers():
    """测试请求头"""
    
    logger.info("=== 请求头验证 ===")
    
    headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'Faces-Request': 'partial/ajax',  # 关键头
        'Host': 'aswbe-i.ana.co.jp',
        'Origin': 'https://aswbe-i.ana.co.jp',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    }
    
    logger.info("关键请求头:")
    critical_headers = ['Content-Type', 'Faces-Request', 'Accept']
    for header in critical_headers:
        logger.info(f"  {header}: {headers[header]}")
    
    logger.info("\n✅ 请求头完整，包含所有JSF AJAX必需的头部")

if __name__ == "__main__":
    logger.info("开始测试修复后的延长按钮功能...")
    
    # 测试payload构建
    payload = test_button_payload()
    
    # 对比HTML按钮
    compare_with_real_html()
    
    # 测试时间参数
    test_timing()
    
    # 测试请求头
    test_headers()
    
    logger.info("\n=== 修复总结 ===")
    logger.info("🔧 主要修复内容:")
    logger.info("1. 修正按钮字段名称为真实的HTML结构")
    logger.info("2. 修正partial.event为'action'（不是'click'）")
    logger.info("3. 添加按钮本身的name=value字段")
    logger.info("4. 修正等待时间为520秒（8分40秒）")
    logger.info("5. 保持完整的JSF AJAX请求结构")
    
    logger.info("\n🎯 预期效果:")
    logger.info("- 服务器应该接受延长请求（不再拒绝）")
    logger.info("- 会话应该成功延长10分钟")
    logger.info("- 最多可延长9次，总计125分钟")
    
    logger.info("\n🚀 测试建议:")
    logger.info("1. 运行修复后的ana_search.py")
    logger.info("2. 等待程序成功占位")
    logger.info("3. 观察8分40秒后的第一次延长请求")
    logger.info("4. 检查服务器响应是否包含XML更新")
    
    logger.success("按钮payload测试完成！")
