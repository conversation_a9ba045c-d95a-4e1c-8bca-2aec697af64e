#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试ANA会话延长功能的修复
"""

import time
from loguru import logger
from curl_cffi import Session
from bs4 import BeautifulSoup

# 配置日志
logger.add("test_session_extend.log", rotation="10 MB", level="DEBUG")

def test_session_extend_payload():
    """测试会话延长请求的payload构建"""
    
    # 模拟从页面提取的表单信息（基于真实抓包数据）
    form_id = "sessionKeeperContainer:j_idt170"
    operation_ticket = "xylhwx+b0f639559e987898b6c68a3af6cc5259~E2sfxS4F7EyykbQyViykJ7uDML_JmQzz7CTvsZcR!1750134816402.aere-xml-controller-8467bf7fd6-fssk4"
    page_ticket = "5"
    view_state = "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"
    action_url = "https://aswbe-i.ana.co.jp/rei21g/international_asw/pages/common/password_input.xhtml?rand=20250617133412R-d8KXG7hw"
    
    # 构建修复后的JSF AJAX payload
    payload = {
        form_id: form_id,
        f'{form_id}_operationTicket': operation_ticket,
        f'{form_id}_cmnPageTicket': page_ticket,
        'javax.faces.ViewState': view_state,
        # 关键的JSF AJAX字段（根据真实按钮HTML和onclick事件）
        'javax.faces.source': 'sessionKeeperContainer:cmnSessionKeepingButton',
        'javax.faces.partial.event': 'action',  # 从onclick看是action事件
        'javax.faces.partial.execute': 'sessionKeeperContainer:cmnSessionKeepingButton',
        'javax.faces.behavior.event': 'action',
        'javax.faces.partial.ajax': 'true',
        # 添加按钮本身的字段
        'sessionKeeperContainer:cmnSessionKeepingButton': '延長'
    }
    
    headers = {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'Faces-Request': 'partial/ajax',
        'Host': 'aswbe-i.ana.co.jp',
        'Origin': 'https://aswbe-i.ana.co.jp',
        'Referer': action_url,
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-dtpc': '1$534850600_325h6vDPMMVBRMAMOFFPKHEOWPJNWRDARLAQCR-0e0',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"'
    }
    
    logger.info("=== 修复后的会话延长请求payload ===")
    logger.info(f"URL: {action_url}")
    logger.info("Headers:")
    for key, value in headers.items():
        logger.info(f"  {key}: {value}")
    
    logger.info("Payload:")
    for key, value in payload.items():
        display_value = value[:50] + "..." if len(str(value)) > 50 else value
        logger.info(f"  {key}: {display_value}")
    
    logger.info("=== 关键修复点 ===")
    logger.info("1. 添加了JSF AJAX必需的字段:")
    logger.info("   - javax.faces.source")
    logger.info("   - javax.faces.partial.event")
    logger.info("   - javax.faces.partial.execute")
    logger.info("   - javax.faces.behavior.event")
    logger.info("   - javax.faces.partial.ajax")
    logger.info("2. 添加了按钮本身的字段:")
    logger.info("   - sessionKeeperContainer:cmnSessionKeepingButton")
    logger.info("3. 保持了正确的Content-Type和Faces-Request头")
    
    return payload, headers, action_url

if __name__ == "__main__":
    logger.info("开始测试ANA会话延长功能修复...")
    test_session_extend_payload()
    logger.info("测试完成！")
